package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for SYRIX-69: Restrict Teams meeting presentation capabilities to organizers only
 * Implements CIS Microsoft 365 Foundation Benchmark 8.5.6
 * CISA Policy ID: MS.TEAMS.4.2v1
 */
@PolicyRemediator("MS.TEAMS.4.2v1")
public class TeamsMeetingPresentationRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

    private static final String POLICY_ID = "MS.TEAMS.4.2v1";
    private static final String TARGET_PRESENTER_SETTING = "OrganizerOnlyUserOverride";
    private static final String PARAMETER_NAME = "designatedPresenterRoleMode";

    public TeamsMeetingPresentationRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
        super(client, configNode, remediationConfig);
    }

    public TeamsMeetingPresentationRemediator(PowerShellTeamsClient client) {
        super(client, null, null);
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(jsonMapper::valueToTree);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        logger.info("Starting Teams meeting presentation remediation for policy: {}", POLICY_ID);
        
        List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

        if (policies.isEmpty()) {
            logger.error("No meeting policies found in configuration");
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(POLICY_ID, "No meeting policies found in configuration"));
        }

        List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
                .filter(this::requiresRemediation)
                .filter(policy -> !policy.identity.startsWith("Tag:")) // Skip read-only Tag policies
                .map(this::fixPolicy_)
                .toList();

        if (results.isEmpty()) {
            logger.info("All meeting policies already comply with presenter restrictions");
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(POLICY_ID));
        }

        logger.info("Found {} meeting policies requiring remediation", results.size());
        return combineResults(results);
    }

    /**
     * Determines if a policy requires remediation based on presenter settings
     * @param policy The Teams meeting policy to check
     * @return true if remediation is needed
     */
    private boolean requiresRemediation(TeamsMeetingPolicy policy) {
        // Check if designatedPresenterRoleMode is not set to the target value
        // For SYRIX-69, only "OrganizerOnlyUserOverride" is considered compliant
        if (policy.designatedPresenterRoleMode == null) {
            return true; // Needs configuration
        }
        
        // Only "OrganizerOnlyUserOverride" is compliant - everything else needs remediation
        return !TARGET_PRESENTER_SETTING.equals(policy.designatedPresenterRoleMode);
    }

    /**
     * Fixes a single meeting policy to restrict presentation to organizers only
     * @param policy The policy to fix
     * @return CompletableFuture with the result
     */
    private CompletableFuture<PolicyChangeResult> fixPolicy_(TeamsMeetingPolicy policy) {
        logger.info("Fixing meeting policy: {} - Current presenter mode: {}", 
                   policy.identity, policy.designatedPresenterRoleMode);

        MeetingPolicy meetingPolicy = new MeetingPolicy();
        meetingPolicy.identity = policy.identity;
        meetingPolicy.designatedPresenterRoleMode = TARGET_PRESENTER_SETTING;

        ParameterChangeResult presenterParam = new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter(PARAMETER_NAME + ": " + policy.identity)
                .prevValue(policy.designatedPresenterRoleMode)
                .newValue(TARGET_PRESENTER_SETTING);

        return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
                .thenApply(meetingPolicies -> {
                    presenterParam.status(ParameterChangeStatus.SUCCESS);
                    logger.info("Successfully updated meeting policy: {} to restrict presenters", policy.identity);
                    return IPolicyRemediator.success_(POLICY_ID, "Meeting Policy fixed: " + policy.identity, List.of(presenterParam));
                })
                .exceptionally(ex -> {
                    logger.warn("Failed to update Meeting Policy: {} , errMsg: {}", policy.identity, ex.getMessage());
                    presenterParam.status(ParameterChangeStatus.FAILED);
                    return IPolicyRemediator.failed_(POLICY_ID, "Failed to update Meeting Policy: " + ex.getMessage(), List.of(presenterParam));
                });
    }

    /**
     * Combines multiple policy change results into a single consolidated result
     * @param futures List of future policy change results
     * @return Consolidated result
     */
    private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<PolicyChangeResult> results = futures.stream()
                            .map(CompletableFuture::join)
                            .toList();

                    List<ParameterChangeResult> allChanges = results.stream()
                            .map(PolicyChangeResult::getChanges)
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .filter(Objects::nonNull)
                            .toList();

                    // Count results by type
                    long successCount = results.stream()
                            .filter(r -> r.getResult() == RemediationResult.SUCCESS)
                            .count();
                    long failedCount = results.stream()
                            .filter(r -> r.getResult() == RemediationResult.FAILED)
                            .count();

                    // Determine overall result status
                    if (failedCount == 0) {
                        logger.info("Successfully remediated all {} meeting policies for presenter restrictions", successCount);
                        return IPolicyRemediator.success_(POLICY_ID, "All meeting policies fixed successfully", allChanges);
                    } else if (successCount == 0) {
                        logger.error("Failed to remediate any meeting policies for presenter restrictions");
                        return IPolicyRemediator.failed_(POLICY_ID, "Failed to fix any meeting policies", allChanges);
                    } else {
                        logger.warn("Partial success: {} policies fixed, {} policies failed", successCount, failedCount);
                        return IPolicyRemediator.partial_success_(POLICY_ID,
                                "Fixed " + successCount + " policies, failed to fix " + failedCount + " policies",
                                allChanges);
                    }
                });
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        logger.info("Starting rollback for Teams meeting presentation policy: {}", POLICY_ID);
        
        try {
            List<ParameterChangeResult> changes = fixResult.getChanges();
            List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

            for (ParameterChangeResult change : changes) {
                String parameter = change.getParameter();
                if (parameter == null || !parameter.contains(": ")) {
                    logger.error("Rollback for policy {} skipped due to invalid parameter format: {}", POLICY_ID, parameter);
                    continue;
                }
                String identity = parameter.split(": ", 2)[1];
                String originalPresenterMode = change.getPrevValue() != null ? change.getPrevValue().toString() : null;
                String currentPresenterMode = change.getNewValue() != null ? change.getNewValue().toString() : TARGET_PRESENTER_SETTING;

                MeetingPolicy meetingPolicy = new MeetingPolicy();
                meetingPolicy.identity = identity;
                meetingPolicy.designatedPresenterRoleMode = originalPresenterMode;

                ParameterChangeResult rollbackParam = new ParameterChangeResult()
                        .timeStamp(Instant.now())
                        .parameter(PARAMETER_NAME + ": " + identity)
                        .prevValue(currentPresenterMode)
                        .newValue(originalPresenterMode);

                if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
                    logger.error("Rollback the policy: {} identity: {} skipped - original change failed", POLICY_ID, identity);
                    rollbackParam.status(ParameterChangeStatus.FAILED);
                    results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(POLICY_ID,
                            "Rollback the policy " + POLICY_ID + " identity: " + identity + " skipped", List.of(rollbackParam))));
                    continue;
                }

                CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
                        .thenApply(jsonNode -> {
                            rollbackParam.status(ParameterChangeStatus.SUCCESS);
                            logger.info("Successfully rolled back meeting presentation policy for: {}", identity);
                            return IPolicyRemediator.success_(POLICY_ID, "Successfully rolled back meeting presentation policy", List.of(rollbackParam));
                        })
                        .exceptionally(ex -> {
                            rollbackParam.status(ParameterChangeStatus.FAILED);
                            logger.error("Exception during meeting presentation policy rollback for: {}", identity, ex);
                            return IPolicyRemediator.failed_(POLICY_ID, ex.getMessage(), List.of(rollbackParam));
                        });

                results.add(result);
            }

            return combineResults(results);
        } catch (Exception ex) {
            logger.error("Rollback the policy {} failed", POLICY_ID, ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(POLICY_ID, ex.getMessage()));
        }
    }
}